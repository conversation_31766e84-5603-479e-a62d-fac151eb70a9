<?php

namespace App\Filament\Resources\DepositAccountResource\Widgets;

use App\Models\DepositAccount;
use Filament\Facades\Filament;
use Filament\Widgets\ChartWidget;

class AccountsByBankChart extends ChartWidget
{
    protected static ?string $heading = 'Accounts by Bank';
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $maxHeight = '300px';

    protected function getData(): array
    {
        // Get current tenant to filter data
        $tenant = Filament::getTenant();

        $data = DepositAccount::with('bankLocation.bank')
            ->where('team_id', $tenant->id) // Filter by current tenant
            ->get()
            ->groupBy('bankLocation.bank.bank_name')
            ->map(fn($group) => $group->count())
            ->sortDesc();

        return [
            'datasets' => [
                [
                    'label' => 'Number of Accounts',
                    'data' => $data->values()->toArray(),
                    'backgroundColor' => '#10b981',
                ],
            ],
            'labels' => $data->keys()->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}

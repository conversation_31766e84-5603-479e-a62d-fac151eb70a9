<?php

namespace App\Filament\Resources\BankResource\Widgets;

use App\Models\Bank;
use App\Models\BankLocation;
use Filament\Facades\Filament;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class BankStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        // Get current tenant to filter data
        $tenant = Filament::getTenant();

        // Show banks that this tenant actually uses (has deposit accounts with)
        $banksUsedByTenant = Bank::whereHas('branches.depositAccounts', function ($query) use ($tenant) {
            $query->where('team_id', $tenant->id);
        })->count();

        $locationsUsedByTenant = BankLocation::whereHas('depositAccounts', function ($query) use ($tenant) {
            $query->where('team_id', $tenant->id);
        })->count();

        return [
            Stat::make('Banks Used', $banksUsedByTenant)
                ->description('Banks with your accounts')
                ->descriptionIcon('heroicon-m-building-library')
                ->color('success'),

            Stat::make('Total Banks', Bank::count())
                ->description('All registered banks')
                ->descriptionIcon('heroicon-m-map-pin')
                ->color('info'),

            Stat::make('Locations Used', $locationsUsedByTenant)
                ->description('Branches with your accounts')
                ->descriptionIcon('heroicon-m-building-office')
                ->color('warning'),
        ];
    }
}

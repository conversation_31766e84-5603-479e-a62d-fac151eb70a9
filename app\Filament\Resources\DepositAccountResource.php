<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DepositAccountResource\Pages;
use App\Filament\Resources\DepositAccountResource\RelationManagers;
use App\Filament\Resources\DepositAccountResource\RelationManagers\BanksRelationManager;
use App\Models\DepositAccount;
use App\Models\BankLocation;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DepositAccountResource extends Resource
{
    protected static ?string $model = DepositAccount::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationGroup = 'Configuration';

    protected static ?int $navigationSort = 4;

    // protected static ?string $tenantOwnershipRelationshipName = 'team';

    public static function form(Form $form): Form
    {
        return $form
        ->schema([
            Section::make('Deposit Account Information')
            ->description('This is the top level resource for managing all your deposit accounts.')
            ->schema([
                Select::make('bank_location_id')
                ->label('Bank Location')
                ->relationship('bankLocation', 'id')
                ->getOptionLabelFromRecordUsing(fn ($record) => $record->bank->bank_name . ' - ' . $record->branch->branch_name)
                ->required()
                ->searchable(['banks.bank_name', 'branches.branch_name'])
                ->preload()
                ->columnSpan('1'),
                TextInput::make('account_number')
                ->label('Account Number')
                ->required()
                ->maxLength(20)
                ->unique(ignoreRecord: true)
                ->columnSpan('1'),
                Toggle::make('is_active')
                ->label('Active')
                ->default(true)
                // ->inline(false)
                ->columnSpan('1'),
            ])->columns(2),

        ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('company.company_name')
                ->label('Company Name')
                ->sortable()
                ->searchable()
                ->toggleable(),
                TextColumn::make('bankLocation.bank.bank_name')
                ->label('Bank')
                ->sortable()
                ->searchable()
                ->toggleable(),
                TextColumn::make('bankLocation.branch.branch_name')
                ->label('Branch')
                ->sortable()
                ->searchable()
                ->toggleable(),
                TextColumn::make('account_number')
                ->label('Account Number')
                ->sortable()
                ->searchable()
                ->toggleable(),
                IconColumn::make('is_active')
                ->label('Active')
                ->boolean()
                ->sortable()
                ->toggleable(),
                TextColumn::make('created_at')
                ->label('Created At')
                ->dateTime()
                ->sortable()
                ->searchable()
                ->toggleable(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDepositAccounts::route('/'),
            'create' => Pages\CreateDepositAccount::route('/create'),
            'edit' => Pages\EditDepositAccount::route('/{record}/edit'),
        ];
    }
}

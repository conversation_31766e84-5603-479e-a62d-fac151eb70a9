<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Fixed Deposits Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 15px;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h2 {
            color: #800000;
            margin-bottom: 5px;
        }

        .header p {
            margin: 3px 0;
            color: #800000;
        }

        .filters {
            text-align: center;
            margin-bottom: 10px;
            font-size: 10px;
            font-style: italic;
        }

        /* Page break styles for mPDF */
        .page-break {
            page-break-before: always;
        }

        .company-header-subsequent {
            text-align: center;
            font-size: 12px;
            font-weight: bold;
            color: #800000;
            margin: 20px 0 15px 0;
        }



        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            border: 1px solid #000;
        }

        thead {
            display: table-header-group;
        }

        th {
            background-color: #f2f2f2;
            border: 1px solid #000;
            padding: 5px;
            text-align: left;
            font-weight: bold;
        }

        .multi-header th {
            border-right: 1px solid #000;
        }

        .multi-header th:last-child {
            border-right: none;
        }

        .sub-header th {
            border-top: 1px solid #000;
            font-size: 11px;
        }

        th.text-center {
            text-align: center;
        }

        td.text-center {
            text-align: center;
        }

        td {
            padding: 5px;
            text-align: left;
            border: none;
        }

        .accrual-border {
            border-left: 1px solid #000;
            font-size: 10px;
        }

        .footer {
            margin-top: 20px;
            text-align: right;
            font-size: 10px;
        }

        .summary {
            margin-top: 20px;
            font-weight: bold;
        }

        @page {
            margin: 15mm 10mm 15mm 10mm;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>FIXED DEPOSITS REPORT</h1>
        <h2><?php echo e($companyInfo); ?></h2>
        <p><?php echo e($depositType); ?></p>
    </div>

    <?php if(isset($filters) && count($filters) > 0): ?>
        <div class="filters">
            <?php $__currentLoopData = $filters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $filter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <span><?php echo e($filter); ?></span>
                <?php if(!$loop->last): ?>
                    |
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php endif; ?>

    <table>
        <thead style="display: table-header-group">
            <!-- Main header row -->
            <tr class="multi-header">
                <th rowspan="2" class="text-center">Start Date</th>
                <th rowspan="2" class="text-center">Maturity Date</th>
                <th rowspan="2">Bank</th>
                <th rowspan="2">Branch</th>
                <th rowspan="2" class="text-center">Principal (RM)</th>
                <th rowspan="2" class="text-center">Rate (%)</th>
                <th rowspan="2" class="text-center">Profit Sharing (Client/Bank)</th>
                <th rowspan="2" class="text-center">Tenure (Days)</th>
                <th rowspan="2">Receipt No.</th>
                <th rowspan="2" class="text-center">Profit Client (RM)</th>
                <th colspan="2" class="text-center">Accrual</th>
            </tr>
            <!-- Sub header row for Accrual columns -->
            <tr class="sub-header">
                <th class="text-center">Day</th>
                <th class="text-center">Profit (RM)</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $records; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $deposit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                
                <?php if($index > 0 && $index % 15 == 0): ?>
        </tbody>
    </table>

    <div class="page-break">
        <div class="company-header-subsequent">
            <?php if(is_array($companyInfo)): ?>
                <?php echo e($companyInfo['name'] ?? 'All Companies'); ?>

            <?php else: ?>
                <?php echo e($companyInfo); ?>

            <?php endif; ?>
        </div>

        <table>
            <thead style="display: table-header-group">
                <!-- Main header row -->
                <tr class="multi-header">
                    <th rowspan="2" class="text-center">Start Date</th>
                    <th rowspan="2" class="text-center">Maturity Date</th>
                    <th rowspan="2">Bank</th>
                    <th rowspan="2">Branch</th>
                    <th rowspan="2" class="text-center">Principal (RM)</th>
                    <th rowspan="2" class="text-center">Rate (%)</th>
                    <th rowspan="2" class="text-center">Profit Sharing (Client/Bank)</th>
                    <th rowspan="2" class="text-center">Tenure (Days)</th>
                    <th rowspan="2">Receipt No.</th>
                    <th rowspan="2" class="text-center">Profit Client (RM)</th>
                    <th colspan="2" class="text-center">Accrual</th>
                </tr>
                <!-- Sub header row for Accrual columns -->
                <tr class="sub-header">
                    <th class="text-center">Day</th>
                    <th class="text-center">Profit (RM)</th>
                </tr>
            </thead>
            <tbody>
    </div>
    <?php endif; ?>

    <tr>
        <td class="text-center"><?php echo e($deposit->start_date->format('d/m/Y')); ?></td>
        <td class="text-center"><?php echo e($deposit->maturity_date->format('d/m/Y')); ?></td>
        <td><?php echo e($deposit->depositAccount->bankLocation->bank->bank_name ?? 'N/A'); ?></td>
        <td><?php echo e($deposit->depositAccount->bankLocation->branch->branch_name ?? 'N/A'); ?></td>
        <td class="text-center"><?php echo e(number_format($deposit->principal_amount, 2)); ?></td>
        <td class="text-center"><?php echo e(number_format($deposit->interest_rate, 2)); ?></td>
        <td class="text-center">
            <?php echo e($deposit->profit_sharing_client); ?>/<?php echo e(100 - $deposit->profit_sharing_client); ?></td>
        <td class="text-center"><?php echo e($deposit->tenure_days); ?></td>
        <td><?php echo e($deposit->receipt_number); ?></td>
        <td class="text-center"><?php echo e(number_format($deposit->client_profit_amount, 2)); ?></td>
        <?php if($cutoffDate): ?>
            <td class="text-center accrual-border"><?php echo e($deposit->getAccrualDays($cutoffDate)); ?></td>
            <td class="text-center accrual-border">
                <?php echo e(number_format($deposit->getAccrualProfit($cutoffDate), 2)); ?>

            </td>
        <?php else: ?>
            <td class="text-center accrual-border">-</td>
            <td class="text-center accrual-border">-</td>
        <?php endif; ?>
    </tr>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </tbody>
    </table>

    <div class="summary">
        <p>Total Deposits: <?php echo e($records->count()); ?></p>
        <p>Total Principal: RM <?php echo e(number_format($records->sum('principal_amount'), 2)); ?></p>
        <p>Total Client Profit: RM <?php echo e(number_format($records->sum('client_profit_amount'), 2)); ?></p>
    </div>

    <div class="footer">
        <p>This is a computer-generated document. No signature is required.</p>
    </div>
</body>

</html>
<?php /**PATH C:\PhpWebStudy-Data\server\www\fdeposit\resources\views/pdfs/fixed-deposits.blade.php ENDPATH**/ ?>
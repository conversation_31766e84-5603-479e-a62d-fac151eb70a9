<?php

namespace App\Filament\Widgets;

use App\Models\FixedDeposit;
use Filament\Facades\Filament;
use Filament\Widgets\ChartWidget;

class InterestRateTrendsWidget extends ChartWidget
{
    protected static ?string $heading = 'Interest Rate Trends (Last 12 Months)';
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        // Get current tenant to filter data
        $tenant = Filament::getTenant();

        $months = collect();
        for ($i = 11; $i >= 0; $i--) {
            $months->push(now()->subMonths($i));
        }

        $mudharabahData = [];
        $conventionalData = [];
        $labels = [];

        foreach ($months as $month) {
            $monthStart = $month->startOfMonth()->toDateString();
            $monthEnd = $month->endOfMonth()->toDateString();

            // Base query with tenant filtering
            $baseQuery = FixedDeposit::whereHas('depositAccount', function ($query) use ($tenant) {
                $query->where('team_id', $tenant->id);
            })->whereBetween('start_date', [$monthStart, $monthEnd]);

            $mudharabahAvg = $baseQuery->where('deposit_type', 'MUDHARABAH')
                ->avg('interest_rate') ?: 0;

            $conventionalAvg = $baseQuery->where('deposit_type', 'CONVENTIONAL')
                ->avg('interest_rate') ?: 0;

            $mudharabahData[] = round($mudharabahAvg, 2);
            $conventionalData[] = round($conventionalAvg, 2);
            $labels[] = $month->format('M Y');
        }

        return [
            'datasets' => [
                [
                    'label' => 'Mudharabah',
                    'data' => $mudharabahData,
                    'borderColor' => '#10B981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'fill' => true,
                ],
                [
                    'label' => 'Conventional',
                    'data' => $conventionalData,
                    'borderColor' => '#3B82F6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
}

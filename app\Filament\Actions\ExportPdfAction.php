<?php

namespace App\Filament\Actions;

use App\Models\User;
use App\Models\FixedDeposit;
use Filament\Facades\Filament;
use Mpdf\Mpdf;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class ExportPdfAction extends Action
{
    public static function getDefaultName(): string
    {
        return 'exportPdf';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Export PDF');
        $this->icon('heroicon-o-document-text');
        $this->color('danger');

        // Add a modal with filter options
        $this->form([
            // First row - three select fields in one line
            Grid::make(3)
                ->schema([
                    Select::make('deposit_type')
                        ->label('Deposit Type')
                        ->options([
                            'MUDHARABAH' => 'Mudharabah',
                            'CONVENTIONAL' => 'Conventional',
                        ])
                        ->placeholder('All Types'),

                    Select::make('status')
                        ->label('Status')
                        ->options([
                            'ACTIVE' => 'Active',
                            'MATURED' => 'Matured',
                            // 'WITHDRAWN' => 'Withdrawn',
                            // 'ROLLED_OVER' => 'Rolled Over',
                        ])
                        ->placeholder('All Statuses'),

                    // single date picker for cutoff
                    DatePicker::make('cutoff_date')
                        ->label('Date')
                        ->helperText('Only deposits with maturity date on or after this date will be included')
                        ->suffixIcon('heroicon-m-calendar')
                        ->displayFormat('d/m/Y')
                        ->native(false)
                        ->required()
                        ->autofocus(),
                ]),
        ]);

        $this->modalHeading('Export Fixed Deposits to PDF');
        $this->modalDescription('Select filters to apply to the exported data.');
        $this->modalSubmitActionLabel('Generate PDF');

        $this->action(function (array $data) {
            // Get filtered records
            $records = $this->getFilteredRecords($data);

            if ($records->isEmpty()) {
                Notification::make()
                    ->title('No records to export')
                    ->warning()
                    ->send();

                return;
            }

            return $this->generatePdf($records, $data);
        });
    }

    protected function getFilteredRecords(array $data): Collection
    {
        // Start with the base query
        $query = FixedDeposit::query()
            ->with(['team', 'depositAccount.bankLocation.bank', 'depositAccount.bankLocation.branch']);

        /** @var User $user */
        $user = Auth::user();
        $tenant = Filament::getTenant();
        if ($tenant) {
            $query->where('team_id', $tenant->getKey());
        }

        // Apply deposit type filter
        if (!empty($data['deposit_type'])) {
            $query->where('deposit_type', $data['deposit_type']);
        }

        // Apply status filter
        if (!empty($data['status'])) {
            $query->where('status', $data['status']);
        }

        // Apply cutoff date filter - only deposits with maturity date >= cutoff date
        if (!empty($data['cutoff_date'])) {
            $query->where('maturity_date', '>=', $data['cutoff_date']);
        }

        // Get the records
        return $query->get();
    }

    protected function generatePdf(Collection $records, array $filters): mixed
    {
        // Generate a unique filename
        $timestamp = now()->format('Ymd_His');
        $filename = "fixed-deposits_{$timestamp}.pdf";

        $tenant = Filament::getTenant();
        // dump($tenant);
        $companyInfo = null;

        if ($tenant) {
            $companyInfo = $tenant->name . ($tenant->code ? " ({$tenant->code})" : '');
        }

        // Get deposit type from filter or records
        $depositType = match ($filters['deposit_type'] ?? null) {
            'MUDHARABAH' => 'Mudharabah',
            'CONVENTIONAL' => 'Conventional',
            default => (
                $records->pluck('deposit_type')->unique()->count() === 1
                    ? match ($records->first()->deposit_type) {
                        'MUDHARABAH' => 'Mudharabah',
                        'CONVENTIONAL' => 'Conventional',
                        default => $records->first()->deposit_type,
                    }
                    : 'All Types'
            ),
        };

        // Create mPDF instance with landscape orientation
        $mpdf = new Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4-L', // A4 Landscape
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_top' => 30,
            'margin_bottom' => 25,
            'margin_header' => 10,
            'margin_footer' => 10,
        ]);

        // Generate HTML content using the original template
        $html = view('pdfs.fixed-deposits', [
            'records' => $records,
            'companyInfo' => $companyInfo,
            'depositType' => $depositType,
            'filters' => $this->formatFilters($filters),
            'cutoffDate' => $filters['cutoff_date'] ?? null,
        ])->render();

        // Set up a simple header for all pages
        $this->setupSimpleHeader($mpdf, $companyInfo);

        // Write HTML content
        $mpdf->WriteHTML($html);

        // Return the PDF for download
        return response()->streamDownload(
            fn () => print($mpdf->Output('', 'S')),
            $filename
        );
    }

    protected function formatFilters(array $filters): array
    {
        $formatted = [];

        // Format cutoff date for display in the PDF
        if (!empty($filters['cutoff_date'])) {
            $formatted['cutoff_date'] = 'Cutoff Date: ' . date('d/m/Y', strtotime($filters['cutoff_date']));
        }

        // Format status for display
        if (!empty($filters['status'])) {
            $formatted['status'] = 'Status: ' . match ($filters['status']) {
                'ACTIVE' => 'Active',
                'MATURED' => 'Matured',
                'WITHDRAWN' => 'Withdrawn',
                'ROLLED_OVER' => 'Rolled Over',
                default => $filters['status'],
            };
        }

        return $formatted;
    }

    protected function setupSimpleHeader(Mpdf $mpdf, $companyInfo): void
    {
        // Set a clean header without borders for first page
        $header = '
            <div style="font-size: 10px; margin-bottom: 10px;">
                <table width="100%" style="border-collapse: collapse; border: none;">
                    <tr>
                        <td style="text-align: left; border: none; padding: 0; margin: 0;">Generated: ' . now()->format('d/m/Y H:i') . '</td>
                        <td style="text-align: right; border: none; padding: 0; margin: 0;">Page {PAGENO}</td>
                    </tr>
                </table>
                <div style="border-top: 1px solid #ccc; margin-top: 5px;"></div>
            </div>
        ';

        $mpdf->SetHTMLHeader($header);
    }
}







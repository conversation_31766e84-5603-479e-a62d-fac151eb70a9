<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fixed_deposits', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained()->cascadeOnDelete();
            $table->foreignId('deposit_account_id')->constrained('deposit_accounts')->cascadeOnDelete();
            $table->string('receipt_number', 50)->unique();
            $table->decimal('principal_amount', 15, 2)->unsigned();
            $table->decimal('interest_rate', 6, 4)->unsigned();
            $table->integer('tenure_days')->unsigned();
            $table->date('start_date');
            $table->date('maturity_date');
            $table->decimal('profit_sharing_client', 5, 2)->nullable();
            $table->decimal('profit_sharing_bank', 5, 2)->nullable();
            $table->enum('deposit_type', ['MUDHARABAH', 'CONVENTIONAL']); // mudharabah vs conventional
            $table->enum('status', ['ACTIVE', 'MATURED', 'WITHDRAWN', 'ROLLED_OVER'])->default('ACTIVE');
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->index('receipt_number');
            $table->index('start_date');
            $table->index('maturity_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fixed_deposits');
    }
};

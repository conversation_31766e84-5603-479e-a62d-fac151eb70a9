<?php

namespace App\Filament\Resources\TeamManagementResource\Pages;

use App\Filament\Resources\TeamManagementResource;
use App\Models\Team;
use App\Models\User;
use App\Models\Role;
use Filament\Actions;
use Filament\Forms;
use Filament\Resources\Pages\Page;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Notifications\Notification;

class ManageTeamUsers extends Page implements HasTable
{
    use InteractsWithTable;

    protected static string $resource = TeamManagementResource::class;

    protected static string $view = 'filament.resources.team-management-resource.pages.manage-team-users';

    public Team $record;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('assignUser')
                ->label('Assign User to Team')
                ->icon('heroicon-o-user-plus')
                ->form([
                    Forms\Components\Select::make('user_id')
                        ->label('User')
                        ->options(
                            User::whereDoesntHave('companies', function ($query) {
                                $query->where('team_id', $this->record->id);
                            })->pluck('name', 'id')
                        )
                        ->searchable()
                        ->required(),
                    Forms\Components\Select::make('roles')
                        ->label('Roles')
                        ->multiple()
                        ->options(
                            Role::where('team_id', $this->record->id)
                                ->pluck('name', 'id')
                        )
                        ->preload(),
                ])
                ->action(function (array $data): void {
                    $user = User::find($data['user_id']);

                    // Assign user to team
                    $this->record->users()->attach($user->id);

                    // Set tenant context and assign roles
                    if (!empty($data['roles'])) {
                        setPermissionsTeamId($this->record->id);
                        $roles = Role::whereIn('id', $data['roles'])->get();

                        // Assign roles one by one to avoid collection issues
                        foreach ($roles as $role) {
                            $user->assignRole($role);
                        }
                    }

                    Notification::make()
                        ->title('User assigned successfully!')
                        ->success()
                        ->send();
                }),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                User::query()
                    ->whereHas('teams', function ($query) {
                        $query->where('team_id', $this->record->id);
                    })
            )
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('team_roles')
                    ->label('Roles in this Team')
                    ->badge()
                    ->separator(',')
                    ->getStateUsing(function (User $record): array {
                        setPermissionsTeamId($this->record->id);
                        return $record->getRoleNames()->toArray();
                    })
                    ->color('success'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('editRoles')
                    ->label('Edit Roles')
                    ->icon('heroicon-o-pencil')
                    ->form([
                        Forms\Components\Select::make('roles')
                            ->label('Roles')
                            ->multiple()
                            ->options(
                                Role::where('team_id', $this->record->id)
                                    ->pluck('name', 'id')
                            )
                            ->default(function (User $record): array {
                                setPermissionsTeamId($this->record->id);
                                return $record->roles()
                                    ->where('roles.team_id', $this->record->id)
                                    ->pluck('roles.id')
                                    ->toArray();
                            }),
                    ])
                    ->action(function (User $record, array $data): void {
                        setPermissionsTeamId($this->record->id);

                        // Remove all current roles for this team
                        $currentRoles = $record->roles()
                            ->where('roles.team_id', $this->record->id)
                            ->get();

                        // Remove roles one by one to avoid collection issues
                        foreach ($currentRoles as $role) {
                            $record->removeRole($role);
                        }

                        // Assign new roles one by one
                        if (!empty($data['roles'])) {
                            $newRoles = Role::whereIn('id', $data['roles'])->get();
                            foreach ($newRoles as $role) {
                                $record->assignRole($role);
                            }
                        }

                        Notification::make()
                            ->title('Roles updated successfully!')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\Action::make('removeFromTeam')
                    ->label('Remove from Team')
                    ->icon('heroicon-o-user-minus')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->action(function (User $record): void {
                        // Set tenant context
                        setPermissionsTeamId($this->record->id);

                        // Remove all roles for this team
                        $teamRoles = $record->roles()
                            ->where('roles.team_id', $this->record->id)
                            ->get();

                        // Remove roles one by one
                        foreach ($teamRoles as $role) {
                            $record->removeRole($role);
                        }

                        // Remove user from team
                        $this->record->users()->detach($record->id);

                        Notification::make()
                            ->title('User removed from team successfully!')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('removeFromTeam')
                        ->label('Remove from Team')
                        ->icon('heroicon-o-user-minus')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->action(function ($records): void {
                            setPermissionsTeamId($this->record->id);

                            foreach ($records as $user) {
                                // Remove all roles for this team
                                $teamRoles = $user->roles()
                                    ->where('roles.team_id', $this->record->id)
                                    ->get();

                                // Remove roles one by one
                                foreach ($teamRoles as $role) {
                                    $user->removeRole($role);
                                }

                                // Remove user from team
                                $this->record->users()->detach($user->id);
                            }

                            Notification::make()
                                ->title('Users removed from team successfully!')
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }
}

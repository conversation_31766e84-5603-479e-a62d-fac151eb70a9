<?php

namespace App\Filament\Resources\Pages\Tenancy;

use App\Models\Team;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\Tenancy\RegisterTenant;
use Illuminate\Support\Facades\Auth;

class RegisterTeam extends RegisterTenant
{
    public static function getLabel(): string
    {
        return 'Register Team';
    }

    public function form(Form $form): Form
    {
        return $form
        ->schema([
            Section::make('Team Information')
            ->description('This is the top level resource for managing all your teams.')
            ->schema([
                TextInput::make('code')
                ->label('Code')
                ->required()
                ->maxLength(10)
                ->unique(ignoreRecord: true)
                ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                ->columnSpan('1')
                ->placeholder('e.g. AE'),
                TextInput::make('name')
                ->label('Name')
                ->required()
                ->maxLength(255)
                ->unique(ignoreRecord: true)
                ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                ->columnSpan('1')
                ->placeholder('e.g. RISDA ESTATES SDN. BHD.'),
                TextInput::make('slug')
                ->required()
                ->maxLength(255)
                ->unique(ignoreRecord: true)
                ->columnSpan('1')
                ->placeholder('e.g. risda-estates-sdn-bhd'),
                Textarea::make('description')
                ->maxLength(255)
                ->columnSpan('full'),
            ])->columns(2),

        ])->columns(1);
    }

    protected function handleRegistration(array $data): Team
    {
        $team = Team::create($data);

        $team->users()->attach(Auth::user());

        return $team;
    }
}

<?php

namespace App\Filament\Actions;

use App\Models\Team;
use App\Models\User;
use App\Models\FixedDeposit;
use Barryvdh\DomPDF\Facade\Pdf;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class ExportPdfActionOld extends Action
{
    public static function getDefaultName(): string
    {
        return 'exportPdfOld';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Export PDF');
        $this->icon('heroicon-o-document-text');
        $this->color('danger');

        // Add a modal with filter options
        $this->form([
            // First row - three select fields in one line
            Grid::make(4)
                ->schema([
                    Select::make('team_id')
                        ->label('Company')
                        ->options(function () {
                            // Only show teams the current user has access to
                            /** @var User $user */
                            $user = Auth::user();
                            return $user->teams()->pluck('name', 'teams.id');
                        })
                        ->searchable()
                        ->placeholder('All Accessible Companies'),

                    Select::make('deposit_type')
                        ->label('Deposit Type')
                        ->options([
                            'MUDHARABAH' => 'Mudharabah',
                            'CONVENTIONAL' => 'Conventional',
                        ])
                        ->placeholder('All Types'),

                    Select::make('status')
                        ->label('Status')
                        ->options([
                            'ACTIVE' => 'Active',
                            'MATURED' => 'Matured',
                            'WITHDRAWN' => 'Withdrawn',
                            'ROLLED_OVER' => 'Rolled Over',
                        ])
                        ->placeholder('All Statuses'),

                    // single date picker for cutoff
                    DatePicker::make('cutoff_date')
                        ->label('Date')
                        ->helperText('Only deposits with maturity date on or after this date will be included')
                        ->suffixIcon('heroicon-m-calendar')
                        ->displayFormat('d/m/Y')
                        ->native(false)
                        ->required(),
                ]),
        ]);

        $this->modalHeading('Export Fixed Deposits to PDF');
        $this->modalDescription('Select filters to apply to the exported data.');
        $this->modalSubmitActionLabel('Generate PDF');

        $this->action(function (array $data) {
            // Get filtered records
            $records = $this->getFilteredRecords($data);

            if ($records->isEmpty()) {
                Notification::make()
                    ->title('No records to export')
                    ->warning()
                    ->send();

                return;
            }

            return $this->generatePdf($records, $data);
        });
    }

    protected function getFilteredRecords(array $data): Collection
    {
        // Start with the base query
        $query = FixedDeposit::query()
            ->with(['team', 'depositAccount.bankLocation.bank', 'depositAccount.bankLocation.branch']);

        // Apply team-based multi-tenancy - only show accessible teams
        /** @var User $user */
        $user = Auth::user();
        $accessibleTeamIds = $user->teams()->pluck('teams.id');
        $query->whereIn('team_id', $accessibleTeamIds);

        // Apply company filter
        if (!empty($data['team_id'])) {
            $query->where('team_id', $data['team_id']);
        }

        // Apply deposit type filter
        if (!empty($data['deposit_type'])) {
            $query->where('deposit_type', $data['deposit_type']);
        }

        // Apply status filter
        if (!empty($data['status'])) {
            $query->where('status', $data['status']);
        }

        // Apply cutoff date filter - only deposits with maturity date >= cutoff date
        if (!empty($data['cutoff_date'])) {
            $query->where('maturity_date', '>=', $data['cutoff_date']);
        }

        // Get the records
        return $query->get();
    }

    protected function generatePdf(Collection $records, array $filters): mixed
    {
        // Generate a unique filename
        $filename = 'fixed-deposits-' . Str::random(8) . '.pdf';

        // Get company name and code from the first record or from the filter
        $companyInfo = 'All Companies';
        if (!empty($filters['team_id'])) {
            $company = Team::find($filters['team_id']);
            if ($company) {
                $companyInfo = $company->name;
                if ($company->code) {
                    $companyInfo .= " ({$company->code})";
                }
            }
        } elseif ($records->isNotEmpty() && $records->first()->team) {
            $company = $records->first()->team;
            $companyInfo = $company->name;
            if ($company->code) {
                $companyInfo .= " ({$company->code})";
            }
        }

        // Get deposit type from filter or records
        $depositType = 'All Types';
        if (!empty($filters['deposit_type'])) {
            $depositType = match ($filters['deposit_type']) {
                'MUDHARABAH' => 'Mudharabah',
                'CONVENTIONAL' => 'Conventional',
                default => $filters['deposit_type'],
            };
        } elseif ($records->pluck('deposit_type')->unique()->count() === 1) {
            $type = $records->first()->deposit_type;
            $depositType = match ($type) {
                'MUDHARABAH' => 'Mudharabah',
                'CONVENTIONAL' => 'Conventional',
                default => $type,
            };
        }

        // Create the PDF with landscape orientation
        $pdf = Pdf::loadView('pdfs.fixed-deposits', [
            'records' => $records,
            'date' => now()->format('d/m/Y'),
            'companyInfo' => $companyInfo,
            'depositType' => $depositType,
            'filters' => $this->formatFilters($filters),
            'cutoffDate' => $filters['cutoff_date'] ?? null,
        ]);

        $pdf->setOptions(['enable_php' => true]);

        // Set landscape orientation
        $pdf->setPaper('a4', 'landscape');

        // Return the PDF for download
        return response()->streamDownload(
            fn () => print($pdf->output()),
            $filename
        );
    }

    protected function formatFilters(array $filters): array
    {
        $formatted = [];

        // Format cutoff date for display in the PDF
        if (!empty($filters['cutoff_date'])) {
            $formatted['cutoff_date'] = 'Cutoff Date: ' . date('d/m/Y', strtotime($filters['cutoff_date']));
        }

        // Format status for display
        if (!empty($filters['status'])) {
            $formatted['status'] = 'Status: ' . match ($filters['status']) {
                'ACTIVE' => 'Active',
                'MATURED' => 'Matured',
                'WITHDRAWN' => 'Withdrawn',
                'ROLLED_OVER' => 'Rolled Over',
                default => $filters['status'],
            };
        }

        return $formatted;
    }
}







<?php

namespace App\Filament\Resources\BankResource\Widgets;

use App\Models\Bank;
use Filament\Facades\Filament;
use Filament\Widgets\ChartWidget;

class BankDistributionChart extends ChartWidget
{
    protected static ?string $heading = 'Your Banks by Account Distribution';
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $maxHeight = '300px';

    protected function getData(): array
    {
        // Get current tenant to filter data
        $tenant = Filament::getTenant();

        // Show distribution of banks by number of accounts this tenant has with them
        $data = Bank::withCount(['branches as account_count' => function ($query) use ($tenant) {
            $query->join('deposit_accounts', 'bank_locations.id', '=', 'deposit_accounts.bank_location_id')
                  ->where('deposit_accounts.team_id', $tenant->id);
        }])
        ->having('account_count', '>', 0) // Only show banks where tenant has accounts
        ->get()
        ->groupBy('account_count')
        ->map(fn($group) => $group->count())
        ->sortKeys();

        return [
            'datasets' => [
                [
                    'label' => 'Number of Banks',
                    'data' => $data->values()->toArray(),
                    'backgroundColor' => ['#f59e0b', '#3b82f6', '#10b981', '#ef4444'],
                ],
            ],
            'labels' => $data->keys()->map(fn($count) => $count . ' accounts')->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }
}

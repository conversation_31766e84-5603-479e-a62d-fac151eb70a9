<?php

namespace App\Models;

use Filament\Models\Contracts\HasCurrentTenantLabel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Team extends Model implements HasCurrentTenantLabel
{
    protected $fillable = [
        'code',
        'name',
        'slug',
        'description',
    ];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    public function bankLocations(): HasMany
    {
        return $this->hasMany(BankLocation::class);
    }

    public function depositAccounts(): HasMany
    {
        return $this->hasMany(DepositAccount::class);
    }

    public function fixedDeposits(): HasMany
    {
        return $this->hasMany(FixedDeposit::class);
    }

    public function roles(): HasMany
    {
        return $this->hasMany(Role::class);
    }

    public function getCurrentTenantLabel(): string
    {
        return 'Active team';
    }

    // Tenant interface methods
    public function getTenantName(): string
    {
        return $this->name ?? 'Unnamed Team';
    }

    public function getRouteKey()
    {
        return $this->slug;
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}

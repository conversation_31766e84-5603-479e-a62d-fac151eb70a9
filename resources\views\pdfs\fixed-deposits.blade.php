<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Fixed Deposits Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 0;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            margin-bottom: 5px;
            font-size: 20px;
        }

        .header h2 {
            color: #800000;
            margin-bottom: 5px;
            font-size: 16px;
        }

        .header p {
            margin: 3px 0;
            color: #800000;
            font-size: 14px;
        }

        .filters {
            text-align: center;
            margin-bottom: 15px;
            font-size: 10px;
            font-style: italic;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            border: 1px solid #000;
        }

        /* Ensure table headers repeat on each page */
        thead {
            display: table-header-group;
        }

        th {
            background-color: #f2f2f2;
            border: 1px solid #000;
            padding: 5px;
            text-align: left;
            font-weight: bold;
            font-size: 11px;
        }

        .multi-header th {
            border-right: 1px solid #000;
        }

        .multi-header th:last-child {
            border-right: none;
        }

        .sub-header th {
            border-top: 1px solid #000;
            font-size: 10px;
        }

        th.text-center {
            text-align: center;
        }

        td {
            padding: 4px;
            text-align: left;
            border: none;
            font-size: 11px;
            vertical-align: top;
        }

        td.text-center {
            text-align: center;
        }

        .accrual-border {
            border-left: 1px solid #000;
            font-size: 10px;
        }

        .text-right {
            text-align: right;
        }

        .summary {
            margin-top: 15px;
            font-weight: bold;
            font-size: 12px;
        }

        .footer {
            margin-top: 20px;
            text-align: right;
            font-size: 10px;
        }

        /* Prevent page breaks inside table rows */
        tr {
            page-break-inside: avoid;
        }

        /* Allow page breaks after table rows when needed */
        tbody tr {
            page-break-after: auto;
        }

        /* Company header for subsequent pages */
        .company-header-subsequent {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            color: #800000;
            margin: 15px 0;
            padding: 8px 0;
        }

        /* Page break utility */
        .page-break {
            page-break-before: always;
        }

        /* Removed @page rule to allow mPDF headers to work */
    </style>
</head>

<body>
    <!-- Only show full header on first page -->
    <div class="header">
        <h1>FIXED DEPOSITS REPORT</h1>
        <h2>{{ $companyInfo }}</h2>
        <p>{{ $depositType }}</p>
    </div>

    @if (isset($filters) && count($filters) > 0)
        <div class="filters">
            @foreach ($filters as $filter)
                <span>{{ $filter }}</span>
                @if (!$loop->last)
                    |
                @endif
            @endforeach
        </div>
    @endif

    <table>
        <thead>
            <!-- Main header row -->
            <tr class="multi-header">
                <th rowspan="2" class="text-center">Start Date</th>
                <th rowspan="2" class="text-center">Maturity Date</th>
                <th rowspan="2">Bank</th>
                <th rowspan="2">Branch</th>
                <th rowspan="2" class="text-center">Principal (RM)</th>
                <th rowspan="2" class="text-center">Rate (%)</th>
                <th rowspan="2" class="text-center">Profit Sharing (Client/Bank)</th>
                <th rowspan="2" class="text-center">Tenure (Days)</th>
                <th rowspan="2">Receipt No.</th>
                <th rowspan="2" class="text-center">Profit Client (RM)</th>
                <th colspan="2" class="text-center">Accrual</th>
            </tr>
            <!-- Sub header row for Accrual columns -->
            <tr class="sub-header">
                <th class="text-center">Day</th>
                <th class="text-center">Profit (RM)</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($records as $index => $deposit)
                {{-- Add company header after estimated page break (every 18 records) --}}
                @if ($index > 0 && $index % 18 == 0)
        </tbody>
    </table>

    <div class="page-break">
        <div class="company-header-subsequent">
            {{ is_array($companyInfo) ? $companyInfo['name'] ?? 'All Companies' : $companyInfo ?? 'All Companies' }}
        </div>

        <table>
            <thead>
                <!-- Main header row -->
                <tr class="multi-header">
                    <th rowspan="2" class="text-center">Start Date</th>
                    <th rowspan="2" class="text-center">Maturity Date</th>
                    <th rowspan="2">Bank</th>
                    <th rowspan="2">Branch</th>
                    <th rowspan="2" class="text-center">Principal (RM)</th>
                    <th rowspan="2" class="text-center">Rate (%)</th>
                    <th rowspan="2" class="text-center">Profit Sharing (Client/Bank)</th>
                    <th rowspan="2" class="text-center">Tenure (Days)</th>
                    <th rowspan="2">Receipt No.</th>
                    <th rowspan="2" class="text-center">Profit Client (RM)</th>
                    <th colspan="2" class="text-center">Accrual</th>
                </tr>
                <!-- Sub header row for Accrual columns -->
                <tr class="sub-header">
                    <th class="text-center">Day</th>
                    <th class="text-center">Profit (RM)</th>
                </tr>
            </thead>
            <tbody>
    </div>
    @endif

    <tr>
        <td class="text-center">{{ $deposit->start_date->format('d/m/Y') }}</td>
        <td class="text-center">{{ $deposit->maturity_date->format('d/m/Y') }}</td>
        <td>{{ $deposit->depositAccount->bankLocation->bank->bank_name ?? 'N/A' }}</td>
        <td>{{ $deposit->depositAccount->bankLocation->branch->branch_name ?? 'N/A' }}</td>
        <td class="text-center">{{ number_format($deposit->principal_amount, 2) }}</td>
        <td class="text-center">{{ number_format($deposit->interest_rate, 2) }}</td>
        <td class="text-center">
            {{ $deposit->profit_sharing_client }}/{{ 100 - $deposit->profit_sharing_client }}
        </td>
        <td class="text-center">{{ $deposit->tenure_days }}</td>
        <td>{{ $deposit->receipt_number }}</td>
        <td class="text-center">{{ number_format($deposit->client_profit_amount, 2) }}</td>
        @if ($cutoffDate)
            <td class="text-center accrual-border">{{ $deposit->getAccrualDays($cutoffDate) }}</td>
            <td class="text-center accrual-border">
                {{ number_format($deposit->getAccrualProfit($cutoffDate), 2) }}
            </td>
        @else
            <td class="text-center accrual-border">-</td>
            <td class="text-center accrual-border">-</td>
        @endif
    </tr>
    @endforeach
    </tbody>
    </table>

    <div class="summary">
        <p>Total Deposits: {{ $records->count() }}</p>
        <p>Total Principal: RM {{ number_format($records->sum('principal_amount'), 2) }}</p>
        <p>Total Client Profit: RM {{ number_format($records->sum('client_profit_amount'), 2) }}</p>
    </div>

    <div class="footer">
        <p>This is a computer-generated document. No signature is required.</p>
    </div>
</body>

</html>

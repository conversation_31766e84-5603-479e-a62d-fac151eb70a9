<?php

namespace App\Filament\Resources\TeamManagementResource\Pages;

use App\Filament\Resources\TeamManagementResource;
use App\Models\Role;
use Filament\Resources\Pages\CreateRecord;

class CreateTeamManagement extends CreateRecord
{
    protected static string $resource = TeamManagementResource::class;

    protected function afterCreate(): void
    {
        // After creating a team, create default roles for it
        $team = $this->record;

        // Set tenant context
        setPermissionsTeamId($team->id);

        // Create default roles
        $defaultRoles = [
            ['name' => 'super_admin', 'guard_name' => 'web'],
            ['name' => 'panel_user', 'guard_name' => 'web'],
        ];

        foreach ($defaultRoles as $roleData) {
            Role::firstOrCreate([
                'name' => $roleData['name'],
                'guard_name' => $roleData['guard_name'],
                'team_id' => $team->id,
            ]);
        }

        $this->notify('success', 'team created successfully with default roles!');
    }
}

<?php if (isset($component)) { $__componentOriginalb525200bfa976483b4eaa0b7685c6e24 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-widgets::components.widget','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-widgets::widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> 
            Portfolio Summary
         <?php $__env->endSlot(); ?>

        <?php $data = $this->getViewData(); ?>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Overview</h4>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total Deposits:</span>
                        <span class="font-medium"><?php echo e(number_format($data['summary']['total_deposits'])); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total Value:</span>
                        <span class="font-medium">RM <?php echo e(number_format($data['summary']['total_value'], 2)); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Avg Tenure:</span>
                        <span class="font-medium"><?php echo e(number_format($data['summary']['avg_tenure'])); ?> days</span>
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">By Type</h4>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Mudharabah:</span>
                        <span class="font-medium"><?php echo e(number_format($data['summary']['mudharabah_count'])); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Conventional:</span>
                        <span class="font-medium"><?php echo e(number_format($data['summary']['conventional_count'])); ?></span>
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Interest Rates</h4>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Highest:</span>
                        <span class="font-medium"><?php echo e(number_format($data['summary']['highest_rate'], 2)); ?>%</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Lowest:</span>
                        <span class="font-medium"><?php echo e(number_format($data['summary']['lowest_rate'], 2)); ?>%</span>
                    </div>
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $attributes = $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $component = $__componentOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php /**PATH C:\PhpWebStudy-Data\server\www\fdeposit\resources\views/filament/widgets/portfolio-summary-widget.blade.php ENDPATH**/ ?>
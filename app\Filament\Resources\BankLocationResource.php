<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BankLocationResource\Pages;
use App\Filament\Resources\BankLocationResource\RelationManagers;
use App\Models\BankLocation;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BankLocationResource extends Resource
{
    protected static ?string $model = BankLocation::class;

    protected static ?string $navigationIcon = 'heroicon-o-map-pin';

    protected static ?string $navigationGroup = 'Configuration';

    protected static ?int $navigationSort = 3;

    // protected static ?string $tenantOwnershipRelationshipName = 'team';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Bank Location Information')
                ->description('This is the top level resource for managing all your bank locations.')
                ->schema([
                    Select::make('bank_id')
                    ->label('Bank')
                    ->relationship('bank', 'bank_name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->columnSpan('1'),
                    Select::make('branch_id')
                    ->label('Branch')
                    ->relationship('branch', 'branch_name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->columnSpan('1'),
                    TextInput::make('address_line1')
                    ->label('Street & Building')
                    ->required()
                    ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                    ->placeholder('e.g. JALAN AMPANG ULU 3'),
                    TextInput::make('address_line2')
                    ->label('Neighborhood / Township')
                    ->nullable()
                    ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                    ->placeholder('e.g. KAMPUNG BEREMBANG'),
                    TextInput::make('address_line3')
                    ->label('Postcode & City')
                    ->required()
                    ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                    ->placeholder('e.g. 55000 KUALA LUMPUR'),
                    Select::make('address_line4')
                    ->label('State')
                    ->options([
                        'JOHOR' => 'JOHOR',
                        'KEDAH' => 'KEDAH',
                        'KELANTAN' => 'KELANTAN',
                        'MELAKA' => 'MELAKA',
                        'NEGERI SEMBILAN' => 'NEGERI SEMBILAN',
                        'PAHANG' => 'PAHANG',
                        'PERAK' => 'PERAK',
                        'PERLIS' => 'PERLIS',
                        'PULAU PINANG' => 'PULAU PINANG',
                        'SABAH' => 'SABAH',
                        'SARAWAK' => 'SARAWAK',
                        'SELANGOR' => 'SELANGOR',
                        'TERENGGANU' => 'TERENGGANU',
                        'W.P. KUALA LUMPUR' => 'W.P. KUALA LUMPUR',
                        'W.P. LABUAN' => 'W.P. LABUAN',
                        'W.P. PUTRAJAYA' => 'W.P. PUTRAJAYA',
                    ])
                    ->searchable()
                    ->required(),
                    TextInput::make('bank_officer')
                    ->label('Bank Officer')
                    ->nullable()
                    ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                    ->maxLength(20)
                    ->placeholder('e.g. PUAN NOR AZLINA'),
                    TextInput::make('contact_number')
                    ->label('Contact No.')
                    ->tel()
                    ->nullable()
                    ->maxLength(10)
                    ->placeholder('e.g. **********'),
                ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('branch.branch_name')
                    ->label('Branch Name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('bank.bank_code')
                    ->label('Bank Code')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('address_line1')
                    ->label('Address 1')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('address_line2')
                    ->label('Address 2')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('address_line3')
                    ->label('Address 3')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('address_line4')
                    ->label('Address 4')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('bank_officer')
                    ->label('Bank Officer')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('contact_number')
                    ->label('Contact No.')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created At')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBankLocations::route('/'),
            'create' => Pages\CreateBankLocation::route('/create'),
            'edit' => Pages\EditBankLocation::route('/{record}/edit'),
        ];
    }
}


<?php

namespace App\Filament\Widgets;

use App\Models\FixedDeposit;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\DB;
use Filament\Widgets\Widget;

class PortfolioSummaryWidget extends Widget
{
    protected static string $view = 'filament.widgets.portfolio-summary-widget';
    protected static ?int $sort = 5;
    protected int | string | array $columnSpan = 'full';

    public function getViewData(): array
    {
        // Get current tenant to filter data
        $tenant = Filament::getTenant();

        // Base query with tenant filtering
        $baseQuery = FixedDeposit::whereHas('depositAccount', function ($query) use ($tenant) {
            $query->where('team_id', $tenant->id);
        })->where('status', 'ACTIVE');

        $summary = [
            'total_deposits' => $baseQuery->count(),
            'total_value' => $baseQuery->sum('principal_amount'),
            'mudharabah_count' => $baseQuery->where('deposit_type', 'MUDHARABAH')->count(),
            'conventional_count' => $baseQuery->where('deposit_type', 'CONVENTIONAL')->count(),
            'avg_tenure' => $baseQuery->avg('tenure_days'),
            'highest_rate' => $baseQuery->max('interest_rate'),
            'lowest_rate' => $baseQuery->min('interest_rate'),
        ];

        $topBanks = DB::table('fixed_deposits')
            ->join('deposit_accounts', 'fixed_deposits.deposit_account_id', '=', 'deposit_accounts.id')
            ->join('bank_locations', 'deposit_accounts.bank_location_id', '=', 'bank_locations.id')
            ->join('banks', 'bank_locations.bank_id', '=', 'banks.id')
            ->where('fixed_deposits.status', 'ACTIVE')
            ->where('deposit_accounts.team_id', $tenant->id) // Filter by current tenant
            ->select('banks.bank_name', DB::raw('COUNT(*) as count'), DB::raw('SUM(principal_amount) as total'))
            ->groupBy('banks.id', 'banks.bank_name')
            ->orderBy('total', 'desc')
            ->limit(5)
            ->get();

        return [
            'summary' => $summary,
            'top_banks' => $topBanks,
        ];
    }
}

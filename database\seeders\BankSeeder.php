<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BankSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();

        $banks = [
            ['bank_code' => 'AFFIN', 'bank_name' => 'AFFIN BANK BERHAD'],
            ['bank_code' => 'AGRO', 'bank_name' => 'AGROBANK BERHAD'],
            ['bank_code' => 'ALLIANCE', 'bank_name' => 'ALLIANCE BANK MALAYSIA BERHAD'],
            ['bank_code' => 'AMB', 'bank_name' => 'AMBANK BERHAD'],
            ['bank_code' => 'BIMB', 'bank_name' => 'BANK ISLAM MALAYSIA BERHAD'],
            ['bank_code' => 'BNPP', 'bank_name' => 'BNP PARIBAS MALAYSIA'],
            ['bank_code' => 'BMMB', 'bank_name' => 'BANK MUAMALAT MALAYSIA BERHAD'],
            ['bank_code' => 'BSN', 'bank_name' => 'BANK SIMPANAN NASIONAL'],
            ['bank_code' => 'CIMB', 'bank_name' => 'CIMB BANK BERHAD'],
            ['bank_code' => 'CITI', 'bank_name' => 'CITIBANK BERHAD'],
            ['bank_code' => 'DBB', 'bank_name' => 'DEUTSCHE BANK MALAYSIA BERHAD'],
            ['bank_code' => 'HLBB', 'bank_name' => 'HONG LEONG BANK BERHAD'],
            ['bank_code' => 'HSBC', 'bank_name' => 'HSBC BANK MALAYSIA'],
            ['bank_code' => 'JPMC', 'bank_name' => 'J.P. MORGAN CHASE BANK MALAYSIA'],
            ['bank_code' => 'MBB', 'bank_name' => 'MAYBANK BERHAD'],
            ['bank_code' => 'OCBC', 'bank_name' => 'OCBC BANK MALAYSIA BERHAD'],
            ['bank_code' => 'PBB', 'bank_name' => 'PUBLIC BANK BERHAD'],
            ['bank_code' => 'RHB', 'bank_name' => 'RHB BANK BERHAD'],
            ['bank_code' => 'SCB', 'bank_name' => 'STANDARD CHARTERED BANK MALAYSIA BERHAD'],
            ['bank_code' => 'UOB', 'bank_name' => 'UNITED OVERSEAS BANK (MALAYSIA)'],
        ];

        foreach ($banks as &$bank) {
            $bank['created_at'] = $now;
            $bank['updated_at'] = $now;
        }

        DB::table('banks')->insert($banks);
    }
}

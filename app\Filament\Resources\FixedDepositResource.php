<?php

namespace App\Filament\Resources;

use App\Filament\Exports\FixedDepositExporter;
use App\Filament\Actions\ExportPdfAction;
use App\Filament\Resources\FixedDepositResource\Pages;
use App\Models\FixedDeposit;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Actions\ExportBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FixedDepositResource extends Resource
{
    protected static ?string $model = FixedDeposit::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $tenantOwnershipRelationshipName = 'team';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Fixed Deposit Information')
                ->description('This is the top level resource for managing all your fixed deposits.')
                ->schema([
                    Tabs::make('Fixed Deposit Details')
                    ->tabs([
                        Tabs\Tab::make('Basic Information')
                        ->schema([
                            Select::make('deposit_account_id')
                            ->label('Deposit Account')
                            ->relationship('depositAccount', function ($query) {
                                return $query->with(['bankLocation.bank', 'bankLocation.branch']);
                            })
                            ->getOptionLabelFromRecordUsing(function ($record) {
                                return $record->bankLocation->bank->bank_code
                                    . ' - ' . $record->account_number
                                    . ' (' . $record->bankLocation->branch->branch_name . ')';
                            })
                            ->searchable()
                            ->preload()
                            ->required(),
                            TextInput::make('receipt_number')
                            ->label('FD Receipt No.')
                            ->required()
                            ->maxLength(15)
                            ->unique(ignoreRecord: true),
                            TextInput::make('principal_amount')
                            ->numeric()
                            ->prefix('RM')
                            ->required()
                            ->inputMode('decimal'),
                        ])->columns(3),

                        Tabs\Tab::make('Term Details')
                        ->schema([
                            TextInput::make('interest_rate')
                            ->numeric()
                            ->suffix('%')
                            ->required()
                            ->inputMode('decimal'),
                            DatePicker::make('start_date')
                            ->label('Start Date')
                            ->suffixIcon('heroicon-m-calendar')
                            ->required()
                            ->native(false)
                            ->displayFormat('d/m/Y')
                            ->reactive()
                            ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                                self::calculateTenure($set, $get)
                            ),
                            DatePicker::make('maturity_date')
                            ->label('Maturity Date')
                            ->suffixIcon('heroicon-m-calendar')
                            ->required()
                            ->native(false)
                            ->displayFormat('d/m/Y')
                            ->reactive()
                            ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                                self::calculateTenure($set, $get)
                            ),
                            TextInput::make('tenure_days')
                            ->label('Tenure (Days)')
                            ->suffixIcon('heroicon-m-calendar-days')
                            ->required()
                            ->maxLength(10)
                            ->readOnly()
                            ->extraAttributes([
                                'class' => 'opacity-50 pointer-events-none'
                            ])
                            ->helperText('Automatically calculated from start & maturity date'),
                        ])->columns(2),

                        Tabs\Tab::make('Profit Details')
                        ->schema([
                            TextInput::make('profit_sharing_client')
                            ->label('Profit Sharing Client')
                            ->numeric()
                            ->required()
                            ->inputMode('decimal')
                            ->reactive()
                            ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                                self::validateProfitSharing($state, $set, $get)
                            )
                            ->rules([
                                fn (callable $get): \Closure => function (string $attribute, $value, \Closure $fail) use ($get) {
                                    $bankShare = (float) $get('profit_sharing_bank');
                                    $clientShare = (float) $value;
                                    $total = $bankShare + $clientShare;

                                    if ($total <= 0 || $total > 100) {
                                        $fail("Total profit sharing must be greater than 0 and less than or equal to 100%.");
                                    }
                                },
                            ]),
                            TextInput::make('profit_sharing_bank')
                            ->label('Profit Sharing Bank')
                            ->numeric()
                            ->required()
                            ->inputMode('decimal')
                            ->reactive()
                            ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                                self::validateProfitSharing($state, $set, $get)
                            )
                            ->rules([
                                fn (callable $get): \Closure => function (string $attribute, $value, \Closure $fail) use ($get) {
                                    $clientShare = (float) $get('profit_sharing_client');
                                    $bankShare = (float) $value;
                                    $total = $bankShare + $clientShare;

                                    if ($total <= 0 || $total > 100) {
                                        $fail("Total profit sharing must be greater than 0 and less than or equal to 100%.");
                                    }
                                },
                            ]),
                            Radio::make('deposit_type')
                            ->label('Deposit Type')
                            ->options([
                                'MUDHARABAH' => 'Mudharabah',
                                'CONVENTIONAL' => 'Conventional'
                            ])
                            ->inline()
                            ->inlineLabel(false)
                            ->required(),
                        ])->columns(3),

                        Tabs\Tab::make('Additional Information')
                        ->schema([
                            Textarea::make('notes')
                            ->label('Notes')
                            ->columnSpan('full'),
                        ]),
                    ])->columnSpanFull(),
                ])->columns(3)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('start_date')
                ->label('Start Date')
                ->date('d/m/Y')
                ->sortable()
                ->toggleable(),
                TextColumn::make('maturity_date')
                ->label('Maturity Date')
                ->date('d/m/Y')
                ->sortable()
                ->toggleable(),
                TextColumn::make('depositAccount.bankLocation.bank.bank_name')
                ->label('Bank')
                ->searchable()
                ->sortable()
                ->toggleable(),
                TextColumn::make('depositAccount.bankLocation.branch.branch_name')
                ->label('Branch')
                ->searchable()
                ->sortable()
                ->toggleable(),
                TextColumn::make('principal_amount')
                ->label('Principal Amount')
                ->money('myr')
                ->sortable()
                ->toggleable(),
                TextColumn::make('interest_rate')
                ->label('Interest Rate')
                ->formatStateUsing(fn ($state) => number_format($state, 2))
                ->suffix('%')
                ->sortable()
                ->toggleable(),
                TextColumn::make('profit_sharing_client', 'profit_sharing_bank')
                ->label('Profit Sharing (Client/Bank)')
                ->sortable()
                ->toggleable()
                ->formatStateUsing(fn ($state, $record) => "{$record->profit_sharing_client}/{$record->profit_sharing_bank}"),
                TextColumn::make('tenure_days')
                ->label('Tenure (days)')
                ->sortable()
                ->toggleable(),
                TextColumn::make('receipt_number')
                ->label('FD Receipt No.')
                ->searchable()
                ->sortable()
                ->toggleable(),
                TextColumn::make('total_profit_amount')
                    ->label('Total Profit')
                    ->money('myr')
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->orderByRaw('(principal_amount * interest_rate / 365 * tenure_days / 100) ' . $direction);
                    })
                    ->toggleable(),
                TextColumn::make('client_profit_amount')
                    ->label('Client Profit')
                    ->money('myr')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('bank_profit_amount')
                    ->label('Bank Profit')
                    ->money('myr')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('team.name')
                ->label('Company Name')
                ->searchable()
                ->sortable()
                ->toggleable(),
                TextColumn::make('depositAccount.account_number')
                ->label('Account No.')
                ->searchable()
                ->sortable()
                ->toggleable(),
                TextColumn::make('deposit_type')
                ->label('Deposit Type')
                ->badge()
                ->color(function (string $state): string {
                    return match ($state) {
                        'MUDHARABAH' => 'warning',
                        'CONVENTIONAL' => 'info',
                        default => 'primary',
                    };

                })
                ->sortable()
                ->toggleable(),
                TextColumn::make('status')
                ->label('Status')
                ->badge()
                ->color(function (string $state): string {
                    return match ($state) {
                        'ACTIVE' => 'success',
                        'MATURED' => 'danger',
                        'WITHDRAWN' => 'gray',
                        'ROLLED_OVER' => 'info',
                        default => 'primary',
                    };

                })
                ->sortable()
                ->toggleable(),
                TextColumn::make('notes')
                ->label('Notes')
                ->searchable()
                ->sortable()
                ->toggleable(),

            ])
            ->filters([
                SelectFilter::make('deposit_type')
                ->options([
                    'MUDHARABAH' => 'Mudharabah',
                    'CONVENTIONAL' => 'Conventional',
                ]),
                Filter::make('created_at')
                ->form([
                    DatePicker::make('created_from'),
                    DatePicker::make('created_until'),
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query
                        ->when(
                            $data['created_from'],
                            fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                        )
                        ->when(
                            $data['created_until'],
                            fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                        );
                })
                ->indicateUsing(function (array $data): array {
                    $indicators = [];

                    if ($data['created_from'] ?? null) {
                        $indicators[] = Indicator::make('Created from ' . Carbon::parse($data['created_from'])->toFormattedDateString())
                            ->removeField('created_from');
                    }

                    if ($data['created_until'] ?? null) {
                        $indicators[] = Indicator::make('Created until ' . Carbon::parse($data['created_until'])->toFormattedDateString())
                            ->removeField('created_until');
                    }

                    return $indicators;
                })
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->headerActions([
                ExportAction::make()
                    ->label('Export Excel')
                    ->icon('heroicon-m-arrow-down-tray')
                    ->color('success')
                    ->exporter(FixedDepositExporter::class),
                ExportPdfAction::make()
                    ->label('Export PDF')
                    ->icon('heroicon-o-document-text')
                    ->color('danger')
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
                ExportBulkAction::make()
                ->label('Export Selected')
                ->icon('heroicon-m-arrow-down-tray')
                ->color('success')
                ->exporter(FixedDepositExporter::class),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFixedDeposits::route('/'),
            'create' => Pages\CreateFixedDeposit::route('/create'),
            'edit' => Pages\EditFixedDeposit::route('/{record}/edit'),
        ];
    }

    public static function calculateTenure(callable $set, callable $get)
    {
        $startDate = $get('start_date');
        $maturityDate = $get('maturity_date');

        if ($startDate && $maturityDate) {
            $tenure = \Carbon\Carbon::parse($startDate)->diffInDays($maturityDate);
            $set('tenure_days', $tenure);
        } else {
            $set('tenure_days', null);
        }
    }

    public static function validateProfitSharing($state, callable $set, callable $get)
    {
        // Get current values
        $clientShare = (float) $get('profit_sharing_client');
        $bankShare = (float) $get('profit_sharing_bank');
        $total = $clientShare + $bankShare;

        // If we're updating the client share, adjust the bank share
        if ($state === $get('profit_sharing_client')) {
            // If client share is valid (0-100), adjust bank share to make total 100%
            if ($clientShare >= 0 && $clientShare <= 100) {
                // Calculate complementary value (100 - client share)
                $newBankShare = 100 - $clientShare;
                $set('profit_sharing_bank', $newBankShare);
            }
        }
        // If we're updating the bank share, adjust the client share
        else if ($state === $get('profit_sharing_bank')) {
            // If bank share is valid (0-100), adjust client share to make total 100%
            if ($bankShare >= 0 && $bankShare <= 100) {
                // Calculate complementary value (100 - bank share)
                $newClientShare = 100 - $bankShare;
                $set('profit_sharing_client', $newClientShare);
            }
        }
    }
}











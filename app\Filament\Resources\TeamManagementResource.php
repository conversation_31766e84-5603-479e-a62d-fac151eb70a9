<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TeamManagementResource\Pages;
use App\Models\Team;
use App\Models\User;
use App\Models\Role;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Facades\Filament;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Columns\TextColumn;

class TeamManagementResource extends Resource
{
    protected static ?string $model = Team::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $navigationGroup = 'Team Management';

    protected static ?string $navigationLabel = 'Team Management';

    protected static ?string $modelLabel = 'Team';

    protected static ?string $pluralModelLabel = 'Teams';

    // Disable tenant scoping for this resource - we want to see ALL teams
    protected static ?string $tenantOwnershipRelationshipName = null;

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    // Override to show ALL teams
    public static function getEloquentQuery(): Builder
    {
        // Return all teams without any tenant filtering - bypass parent completely
        return static::getModel()::query();
    }

    // Override to disable tenant scoping completely
    public static function isScopedToTenant(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Team Information')
                    ->schema([
                        TextInput::make('code')
                            ->label('Code')
                            ->required()
                            ->maxLength(10)
                            ->unique(ignoreRecord: true),
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        TextInput::make('slug')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        Textarea::make('description')
                            ->maxLength(1000),
                    ])
                    ->columns(2),

                Section::make('User Management')
                    ->schema([
                        Repeater::make('userAssignments')
                            ->relationship('users')
                            ->schema([
                                Select::make('user_id')
                                    ->label('User')
                                    ->options(User::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required(),
                                Select::make('roles')
                                    ->label('Roles')
                                    ->multiple()
                                    ->options(function (callable $get) {
                                        $teamId = $get('../../id');
                                        if (!$teamId) return [];

                                        return Role::where('team_id', $teamId)
                                            ->pluck('name', 'id');
                                    })
                                    ->preload(),
                            ])
                            ->columns(2)
                            ->addActionLabel('Add User')
                            ->collapsible(),
                    ])
                    ->visible(fn ($record) => $record !== null), // Only show when editing existing team
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('slug')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('users_count')
                    ->counts('users')
                    ->label('Users')
                    ->badge()
                    ->color('info'),
                TextColumn::make('roles_count')
                    ->counts('roles')
                    ->label('Roles')
                    ->badge()
                    ->color('success'),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('manageUsers')
                    ->label('Manage Users')
                    ->icon('heroicon-o-users')
                    ->color('info')
                    ->url(fn (Team $record): string => static::getUrl('manageUsers', ['record' => $record])),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTeamManagement::route('/'),
            'create' => Pages\CreateTeamManagement::route('/create'),
            'view' => Pages\ViewTeamManagement::route('/{record}'),
            'edit' => Pages\EditTeamManagement::route('/{record}/edit'),
            'manageUsers' => Pages\ManageTeamUsers::route('/{record}/users'),
        ];
    }
}

<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FixedDeposit extends Model
{
    use HasFactory;

    protected $fillable = [
        'deposit_account_id',
        'receipt_number',
        'principal_amount',
        'interest_rate',
        'tenure_days',
        'start_date',
        'maturity_date',
        'profit_sharing_client',
        'profit_sharing_bank',
        'deposit_type',
        'status',
        'notes',
    ];

    protected $casts = [
        'start_date' => 'date',
        'maturity_date' => 'date',
    ];

    public function depositAccount()
    {
        return $this->belongsTo(DepositAccount::class);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Calculate the total profit amount for this fixed deposit
     *
     * @return float
     */
    public function getTotalProfitAmountAttribute(): float
    {
        // Calculate total profit: Principal * Rate / 365 * Tenure / 100
        return round(
            ($this->principal_amount * $this->interest_rate * $this->tenure_days) /
            (365 * 100),
            2
        );
    }

    /**
     * Calculate the client's share of the profit based on profit sharing percentage
     *
     * @return float
     */
    public function getClientProfitAmountAttribute(): float
    {
        // Calculate client's share of the profit
        return round(
            $this->total_profit_amount * ($this->profit_sharing_client / 100),
            2
        );
    }

    /**
     * Calculate the bank's share of the profit based on profit sharing percentage
     *
     * @return float
     */
    public function getBankProfitAmountAttribute(): float
    {
        // Calculate bank's share of the profit
        return round(
            $this->total_profit_amount * ($this->profit_sharing_bank / 100),
            2
        );
    }

    /**
     * Calculate the number of days for accrual calculation
     * From 1 January of the cutoff year to the cutoff date,
     * unless start_date is later than 1 January
     *
     * @param string $cutoffDate The cutoff date in Y-m-d format
     * @return int
     */
    public function getAccrualDays(string $cutoffDate): int
    {
        $cutoff = Carbon::parse($cutoffDate);
        $startOfYear = Carbon::create($cutoff->year, 1, 1);
        $startDate = Carbon::parse($this->start_date);

        // Use the later of start_date or January 1st of the cutoff year
        $calculationStart = $startDate->greaterThan($startOfYear) ? $startDate : $startOfYear;

        // Calculate days from calculation start to cutoff date
        return $calculationStart->diffInDays($cutoff) + 1; // +1 to include both start and end dates
    }

    /**
     * Calculate the accrual profit amount using the formula:
     * Profit = (Principal × Rate ÷ 365 × Day) ÷ 100
     *
     * @param string $cutoffDate The cutoff date in Y-m-d format
     * @return float
     */
    public function getAccrualProfit(string $cutoffDate): float
    {
        $days = $this->getAccrualDays($cutoffDate);

        // Formula: (Principal × Rate ÷ 365 × Day) ÷ 100
        return round(
            ($this->principal_amount * $this->interest_rate * $days) / (365 * 100),
            2
        );
    }
}




<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FixedDeposit extends Model
{
    use HasFactory;

    protected $fillable = [
        'deposit_account_id',
        'receipt_number',
        'principal_amount',
        'interest_rate',
        'tenure_days',
        'start_date',
        'maturity_date',
        'profit_sharing_client',
        'profit_sharing_bank',
        'deposit_type',
        'status',
        'notes',
    ];

    protected $casts = [
        'start_date' => 'date',
        'maturity_date' => 'date',
    ];

    public function depositAccount()
    {
        return $this->belongsTo(DepositAccount::class);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Calculate the total profit amount for this fixed deposit
     *
     * @return float
     */
    public function getTotalProfitAmountAttribute(): float
    {
        // Calculate total profit: Principal * Rate / 365 * Tenure / 100
        return round(
            ($this->principal_amount * $this->interest_rate * $this->tenure_days) /
            (365 * 100),
            2
        );
    }

    /**
     * Calculate the client's share of the profit based on profit sharing percentage
     *
     * @return float
     */
    public function getClientProfitAmountAttribute(): float
    {
        // Calculate client's share of the profit
        return round(
            $this->total_profit_amount * ($this->profit_sharing_client / 100),
            2
        );
    }

    /**
     * Calculate the bank's share of the profit based on profit sharing percentage
     *
     * @return float
     */
    public function getBankProfitAmountAttribute(): float
    {
        // Calculate bank's share of the profit
        return round(
            $this->total_profit_amount * ($this->profit_sharing_bank / 100),
            2
        );
    }
}




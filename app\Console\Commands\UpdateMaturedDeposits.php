<?php

namespace App\Console\Commands;

use App\Models\FixedDeposit;
use Carbon\Carbon;
use Illuminate\Console\Command;

class UpdateMaturedDeposits extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'deposits:update-matured';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update ACTIVE fixed deposits to MATURED status when maturity date has passed';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $today = Carbon::today();

        // Find all ACTIVE deposits where maturity date is today or earlier
        $maturedDeposits = FixedDeposit::where('status', 'ACTIVE')
            ->where('maturity_date', '<=', $today)
            ->get();

        if ($maturedDeposits->isEmpty()) {
            $this->info('No deposits to update.');
            return 0;
        }

        $count = $maturedDeposits->count();

        // Update status to MATURED
        FixedDeposit::where('status', 'ACTIVE')
            ->where('maturity_date', '<=', $today)
            ->update(['status' => 'MATURED']);

        $this->info("Updated {$count} deposit(s) from ACTIVE to MATURED status.");

        // Log the updated deposits
        foreach ($maturedDeposits as $deposit) {
            $this->line("- Receipt #{$deposit->receipt_number} (Maturity: {$deposit->maturity_date->format('d/m/Y')})");
        }

        return 0;
    }
}

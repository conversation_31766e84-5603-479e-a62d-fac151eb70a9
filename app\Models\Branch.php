<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Branch extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_name',
    ];

    public function banks()
    {
        return $this->belongsToMany(Bank::class, 'bank_locations', 'branch_id', 'bank_id')
                    ->withPivot(['address_1', 'address_2', 'address_3', 'address_4', 'bank_officer', 'contact_number', 'created_at', 'updated_at']);
    }

}
